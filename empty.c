/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/************************* 头文件 *************************/
#include "ti_msp_dl_config.h"
#include "stdio.h"
#include "hw_timer.h"
#include "bsp_tb6612.h"
#include "hw_key.h"
#include "hw_lcd.h"
#include "string.h"
#include "LED.h"
#include "KEY.h"
#include "USART.h"
#include "PWM.h"
#include "IIC.h"
#include "OLED.h"
#include "bsp_motor_hallencoder.h"
#include "PID.h"
#include "Filter.h"
#include "Time.h"
#include "hardware_iic.h"
#include "Sensor.h"

/************************* 宏定义 *************************/
#define BASE_SPEED 130 // 基础速度

#define angle_pid_p 0.05f
#define angle_pid_i 0.0f
#define angle_pid_d 0.1f

#define posion_pid_p 0.0f
#define posion_pid_i 0.0f
#define posion_pid_d 0.0f

#define left_pid_p 5.0f
#define left_pid_i 100.0f
#define left_pid_d 0.001f

#define right_pid_p 5.0f
#define right_pid_i 100.0f
#define right_pid_d 0.001f

Kalman_Filter left_kalman;
Kalman_Filter right_kalman;
Kalman_Filter posion_kalman;
/************************ 变量定义 ************************/
IncrementalPID left_speed_pid;
IncrementalPID right_speed_pid;
IncrementalPID posion_pid;
IncrementalPID angle_pid;

volatile uint8_t led_flash_time = 0;
volatile uint8_t bmq_flash_time = 0;
volatile uint8_t PID_flash_time = 0;
uint16_t temporary_buffer[256]; // 临时缓冲区，共用
uint16_t j = 0;					// 临时变量

/************************ 函数定义 ************************/
void PID_operation(void); // PID操作
void PID_speed_angle(void);
int main(void)
{
	SYSCFG_DL_init();
	// 使能外部中断
	//	NVIC_EnableIRQ (KEY1_INT_IRQN );
	timer_init();
	// 清除定时器中断标志
	NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
	// 使能定时器中断
	NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);

	// 清除串口中断标志
	NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
	// 使能串口中断
	NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
	jy61pInit();

	///////////////////////////////以上用于调试陀螺仪////////////////////////////////

	PID_Init(&left_speed_pid, left_pid_p, left_pid_i, left_pid_d);
	PID_Init(&right_speed_pid, right_pid_p, right_pid_i, right_pid_d);
	PID_Init(&posion_pid, posion_pid_p, posion_pid_i, posion_pid_d);
	PID_Init(&angle_pid, angle_pid_p, angle_pid_i, angle_pid_d);
	// 卡尔曼滤波参数
	Kalman_Init(&posion_kalman, 0.0f, 1.0f, 0.1f, 0.01f);
	Kalman_Init(&left_kalman, 0.0f, 1.0f, 0.1f, 0.01f);
	Kalman_Init(&right_kalman, 0.0f, 1.0f, 0.1f, 0.01f);

	SYSCFG_DL_init();
	// 使能外部中断
	//	NVIC_EnableIRQ (KEY1_INT_IRQN );
	timer_init();
	// 清除定时器中断标志
	NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
	// 使能定时器中断
	NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
	// 清除串口中断标志
	NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
	// 使能串口中断
	NVIC_EnableIRQ(UART_0_INST_INT_IRQN);

	encoder_init();
	SYSCFG_DL_init();
	lcd_init();
	timer_init();

	sprintf((char *)rx_buff, "hello_world!\r\n");
	uart0_send_string((char *)rx_buff);
	memset(rx_buff, 0, 256);
	while (Ping())
	{
		delay_ms(1);
		sprintf((char *)rx_buff, "Ping Faild Try Again!\r\n");
		uart0_send_string((char *)rx_buff);
		memset(rx_buff, 0, 256);
	}
	sprintf((char *)rx_buff, "Ping Succseful!\r\n");
	uart0_send_string((char *)rx_buff);
	memset(rx_buff, 0, 256);

	while (1)
	{

		if (PID_flash_time)
		{
			PID_flash_time = 0;
			// PID_operation();
			// AB_Control(-100,-100);
			PID_speed_angle();
		}
	}
}

// PID操作
void PID_speed_angle(void)
{
	static float total_left_speed = 0;	// 总左移速度
	static float total_right_speed = 0; // 总右移速度
	static float target_angle = 0;		// 目标角度
	static uint8_t angle_init_flag = 0; // 角度初始化标志

	int left_speed = get_encoder_count_L();								  // 获取左编码器速度
	int right_speed = get_encoder_count_R();							  // 获取右编码器速度
	float left_speed_filter = Kalman_Update(&left_kalman, left_speed);	  // 左速度滤波值 = 卡尔曼更新
	float right_speed_filter = Kalman_Update(&right_kalman, right_speed); // 右速度滤波值 = 卡尔曼更新
	Gyro_Struct *JY61P_Data = get_angle();
	float angle = JY61P_Data->z; // 获取角度（使用float保持精度）

	// 初始化目标角度为当前角度（保持直线行驶）
	if (angle_init_flag == 0)
	{
		target_angle = angle;
		angle_init_flag = 1;
	}

	float angle_adjust = PID_Calculate_angle(&angle_pid, 0.02f, target_angle, angle); // 角度调整量

	// 设置基础速度，角度调整量用于修正左右轮速度差
	float left_speed_adjust = BASE_SPEED - angle_adjust;  // 左轮期望速度
	float right_speed_adjust = BASE_SPEED + angle_adjust; // 右轮期望速度

	float adjust_left_speed = PID_Calculate(&left_speed_pid, 0.02f, left_speed_adjust, left_speed_filter);	   // 左轮总速度增量
	float adjust_right_speed = PID_Calculate(&right_speed_pid, 0.02f, right_speed_adjust, right_speed_filter); // 右轮总速度增量

	total_left_speed += adjust_left_speed;
	total_right_speed += adjust_right_speed;

	// 限制输出范围
	total_left_speed = total_left_speed <= 950 ? total_left_speed : 950;
	total_right_speed = total_right_speed <= 950 ? total_right_speed : 950;
	total_left_speed = total_left_speed >= -950 ? total_left_speed : -950;
	total_right_speed = total_right_speed >= -950 ? total_right_speed : -950;

	AB_Control(total_left_speed, total_right_speed);
	Num_L = total_left_speed;
	Num_R = total_right_speed;

	sprintf((char *)rx_buff, "w:%f,%f,%d,%.1f\r\n", angle_adjust, total_left_speed, BASE_SPEED, angle);
	uart_send(rx_buff);
	//////////////////////////////////////////////////////////////////////////////////////////////
}
// PID操作
void PID_operation(void)
{
	static float total_left_speed = 0;	// 总左移速度
	static float total_right_speed = 0; // 总右移速度
	static float total_speed = 0;		// 位置环累积

	float posion = UpdatePosition();		 // 获取位置值
	int left_speed = get_encoder_count_L();	 // 获取左编码器速度
	int right_speed = get_encoder_count_R(); // 获取右编码器速度

	float left_speed_filter = Kalman_Update(&left_kalman, left_speed);	  // 左速度滤波值 = 卡尔曼更新
	float right_speed_filter = Kalman_Update(&right_kalman, right_speed); // 右速度滤波值 = 卡尔曼更新
	float posion_filter = Kalman_Update(&posion_kalman, posion);		  // 位置滤波值

	float adjust = PID_Calculate(&posion_pid, 0.02f, 0, posion_filter); // 调整 = PID计算
	total_speed += adjust;
	float left_speed_adjust = BASE_SPEED + total_speed;	 // 左速度调整 = 基础速度-调整  得到新的期望值
	float right_speed_adjust = BASE_SPEED - total_speed; // 右速度调整 = 基础速度-调整    得到新的期望值

	float adjust_left_speed = PID_Calculate(&left_speed_pid, 0.02f, left_speed_adjust, left_speed_filter);	   // 左轮总速度增量
	float adjust_right_speed = PID_Calculate(&right_speed_pid, 0.02f, right_speed_adjust, right_speed_filter); // 右轮总速度增量

	total_left_speed = total_left_speed + adjust_left_speed;
	total_right_speed = total_right_speed + adjust_right_speed;

	//  float left_speed_filter_adjust=Kalman_Update(&left_kalman,total_left_speed);//左速度滤波值 = 卡尔曼更新
	//	float right_speed_filter_adjust =Kalman_Update(&right_kalman,total_right_speed);//右速度滤波值 = 卡尔曼更新

	//	float	left_speed_adjust=BASE_SPEED-adjust; //左速度调整 = 基础速度-调整
	//	float right_speed_adjust=BASE_SPEED+adjust;//右速度调整 = 基础速度-调整

	AB_Control(total_left_speed, total_right_speed);
	Num_L = total_left_speed;
	Num_R = total_right_speed;
	// printf("w:%f,%f,%d,%d\r\n",left_speed_filter,right_speed_filter,BASE_SPEED,left_speed);
	sprintf((char *)rx_buff, "w:%f,%f,%d,%d\r\n", left_speed_filter, right_speed_filter, BASE_SPEED, left_speed);
	uart_send(rx_buff);
	//////////////////////////////////////////////////////////////////////////////////////////////
}

// 定时器的中断服务函数 已配置为1ms的周期
void TIMER_0_INST_IRQHandler(void)
{

	static uint32_t timer_count = 1;
	j++;
	// 如果产生了定时器中断
	if (DL_TimerG_getPendingInterrupt(TIMER_0_INST) == DL_TIMER_IIDX_ZERO) // 如果是0溢出中断
	{

		timer_count = timer_count < 1000 ? timer_count + 1 : 1;
		if (timer_count % 1000 == 0)
			led_flash_time = 1;
		if (timer_count % 500 == 0)
			bmq_flash_time = 1;
		if (timer_count % 20 == 0)
			PID_flash_time = 1;
	}
}

/****************************End*****************************/

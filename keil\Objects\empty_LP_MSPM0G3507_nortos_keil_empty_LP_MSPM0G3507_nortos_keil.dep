Dependencies for Project 'empty_LP_MSPM0G3507_nortos_keil', Target 'empty_LP_MSPM0G3507_nortos_keil': (DO NOT MODIFY !)
CompilerVersion: 6210000::V6.21::ARMCLANG
F (../empty.c)(0x687348B2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/empty.o -MD)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdio.hkardWear\hw_timer.h)(0x00000000)
I (..\..\empty\ti_msp_dl_config.heardWear\bsp_motor_hallencoder.h)(0x00000000)
I (HardWear\bsp_tb6612.h_ardWear\hw_key.h\ardWear\hw_lcd.h)(0x00000000)
I (C:\Keil_v5\ARM\ARMCLANG\include\string.hrardWear\LED.h)(0x00000000)
I (..\middle\Time.hRardWear\KEY.heardWear\USART.haardWear\PWM.h)(0x00000000)
I (HardWear\IIC.h.ardWear\OLED.hhB6612\PID.hAB6612\Filter.h)(0x00000000)
I (..\middle\hardware_iic.hL.\middle\hw_i2c.hB.\middle\Sensor.h)(0x00000000)
F (../empty.syscfg)(0x6870AA8A)()
F (startup_mspm0g350x_uvision.s)(0x68689624)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4 -Wa,armasm,--pd,"__MICROLIB SETA 1"

-Wa,armasm,--pd,"__UVISION_VERSION SETA 539" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (../ti_msp_dl_config.h)(0x6870AA90)()
F (../ti_msp_dl_config.c)(0x6870AA90)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MD)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (.\HardWear\LED.c)(0x686CC552)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/led.o -MD)
I (..\..\empty\ti_msp_dl_config.hd.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h..\middle\Time.h)(0x00000000)
F (.\HardWear\LED.h)(0x686CC5F2)()
F (.\HardWear\KEY.c)(0x68661204)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/key.o -MD)
I (..\..\empty\ti_msp_dl_config.hd.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h..\middle\Time.h)(0x00000000)
I (HardWear\LED.h)(0x686CC5F2)
F (.\HardWear\KEY.h)(0x686CC5F2)()
F (.\HardWear\USART.c)(0x686E70A4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/usart.o -MD)
I (..\..\empty\ti_msp_dl_config.h..\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
F (.\HardWear\USART.h)(0x686E6FF4)()
F (.\HardWear\PWM.c)(0x68663D6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/pwm.o -MD)
I (..\..\empty\ti_msp_dl_config.he.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h..\middle\Time.h)(0x00000000)
I (HardWear\LED.h)(0x686CC5F2)
F (.\HardWear\PWM.h)(0x686CC5F2)()
F (.\HardWear\iic.c)(0x6870AAD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/iic.o -MD)
I (..\..\empty\ti_msp_dl_config.h|.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h..\middle\Time.h)(0x00000000)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x6569B012)
F (.\HardWear\iic.h)(0x6870AAD2)()
F (.\HardWear\hw_lcd.c)(0x6868A31C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/hw_lcd.o -MD)
I (..\..\empty\ti_msp_dl_config.hc.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h.ardWear\lcdfont.h)(0x00000000)
F (.\HardWear\hw_lcd.h)(0x6868A35C)()
F (.\HardWear\lcdfont.h)(0x6868A3B6)()
F (.\HardWear\OLED.c)(0x6868AAD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/oled.o -MD)
I (..\..\empty\ti_msp_dl_config.h)(0x6870AA90)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h.ardWear\hw_lcd.h)(0x00000000)
I (C:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x6569B012)
F (.\HardWear\OLED.h)(0x6868AAF8)()
F (.\HardWear\hw_key.c)(0x6868D9F4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/hw_key.o -MD)
I (..\..\empty\ti_msp_dl_config.he.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (.\HardWear\hw_key.h)(0x6868D9F4)()
F (.\TB6612\PID.c)(0x68733DFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/pid.o -MD)
F (.\TB6612\PID.h)(0x68733DFF)()
F (.\HardWear\bsp_motor_hallencoder.c)(0x686BD142)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_motor_hallencoder.o -MD)
I (HardWear\bsp_motor_hallencoder.h)(0x686A8050)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (.\HardWear\bsp_motor_hallencoder.h)(0x686A8050)()
F (.\HardWear\bsp_tb6612.c)(0x686BCFFE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_tb6612.o -MD)
I (..\..\empty\ti_msp_dl_config.hb.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x6569B012)
F (.\HardWear\bsp_tb6612.h)(0x686A93BE)()
F (.\HardWear\hw_timer.c)(0x686B69AA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/hw_timer.o -MD)
I (HardWear\bsp_motor_hallencoder.hs.\..\empty\ti_msp_dl_config.h)(0x00000000)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h.ardWear\bsp_tb6612.h)(0x00000000)
F (.\HardWear\hw_timer.h)(0x686B679C)()
F (.\TB6612\Filter.c)(0x686A3B34)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/filter.o -MD)
F (.\TB6612\Filter.h)(0x686A3B34)()
F (..\middle\hardware_iic.c)(0x686E7532)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/hardware_iic.o -MD)
I (..\middle\hardware_iic.h)(0x686E75A8)
I (..\..\empty\ti_msp_dl_config.hd.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\string.h..\middle\Time.h)(0x00000000)
I (HardWear\USART.hR:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x00000000)
I (HardWear\hw_timer.hKardWear\bsp_motor_hallencoder.h)(0x00000000)
F (..\middle\hardware_iic.h)(0x686E75A8)()
F (..\middle\hw_i2c.c)(0x686CC3FC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/hw_i2c.o -MD)
I (..\..\empty\ti_msp_dl_config.h_.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x6569B012)
F (..\middle\hw_i2c.h)(0x686CC49E)()
F (..\middle\Time.c)(0x686E7532)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/time.o -MD)
I (..\..\empty\ti_msp_dl_config.h..\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (..\middle\Time.h)(0x686CC534)()
F (..\middle\Sensor.c)(0x686CDCCA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../empty -I ./HardWear -I ../middle -I ../keil -I ./TB6612 -I ../middle

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/sensor.o -MD)
I (..\..\empty\ti_msp_dl_config.hn.\..\source\ti\devices\msp\msp.h)(0x00000000)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x67C10125)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (F:\NUEDC\M0_SDK\mspm0_sdk_2_04_00_06\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (C:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h..\..\source\ti\driverlib\dl_crc.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.hh.\..\source\ti\driverlib\dl_i2c.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.hl.\..\source\ti\driverlib\dl_opa.h)(0x00000000)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h..\middle\hardware_iic.h)(0x00000000)
I (..\middle\hw_i2c.hi:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x00000000)
I (..\middle\Time.h.ardWear\USART.h)(0x00000000)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
F (..\middle\Sensor.h)(0x686CDCCA)()
